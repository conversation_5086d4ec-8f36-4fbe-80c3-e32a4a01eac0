<template>
	<view>
		<cu-custom bgColor="none" :isSearch="false" :isBack="true">
			<view slot="backText">返回</view>
			<view slot="content" style="color: #000000; font-weight: 600; font-size: 36rpx"></view>
		</cu-custom>

		<view class="map-container">

			<!-- 地图组件 -->
			<map id="map" class="map" :longitude="currentLocation.longitude" :latitude="currentLocation.latitude"
				:scale="13" :markers="markers" :show-location="true" @markertap="onMarkerTap"
				@callouttap="onCalloutTap">
			</map>

			<!-- 加载提示 -->
			<view v-if="loading" class="loading-mask">
				<view class="loading-content">
					<text class="loading-text">正在获取位置信息...</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			loading: true,
			// 当前位置
			currentLocation: {
				longitude: 116.397428, // 默认北京坐标
				latitude: 39.90923
			},
			// 地图标注点数据
			markers: [],
			// 静态圈子数据
			realmData: [
				{
					id: 1,
					realm_icon: 'https://wei.inotnpc.com/2021/11/18/ae4656f0119c62c50e44340ebca4c56a.png',
					realm_name: '摄影爱好者圈',
					longitude: 116.397428,
					latitude: 39.90923
				},
				{
					id: 2,
					realm_icon: 'https://wei.inotnpc.com/2021/11/18/ae4656f0119c62c50e44340ebca4c56a.png',
					realm_name: '户外运动圈',
					longitude: 116.407428,
					latitude: 39.91923
				},
				{
					id: 3,
					realm_icon: 'https://wei.inotnpc.com/2021/11/18/ae4656f0119c62c50e44340ebca4c56a.png',
					realm_name: '美食分享圈',
					longitude: 116.387428,
					latitude: 39.89923
				},
				{
					id: 4,
					realm_icon: 'https://wei.inotnpc.com/2021/11/18/ae4656f0119c62c50e44340ebca4c56a.png',
					realm_name: '读书交流圈',
					longitude: 116.417428,
					latitude: 39.92923
				},
				{
					id: 5,
					realm_icon: 'https://wei.inotnpc.com/2021/11/18/ae4656f0119c62c50e44340ebca4c56a.png',
					realm_name: '音乐爱好圈',
					longitude: 116.377428,
					latitude: 39.88923
				}
			]
		}
	},
	onLoad() {
		this.getCurrentLocation();
	},
	methods: {
		// 获取当前位置
		getCurrentLocation() {
			uni.getLocation({
				type: 'gcj02',
				success: (res) => {
					console.log('获取位置成功:', res);
					this.currentLocation = {
						longitude: res.longitude,
						latitude: res.latitude
					};
					this.initMarkers();
					this.loading = false;
				},
				fail: (err) => {
					console.log('获取位置失败:', err);
					this.loading = false;

					// 弹窗提示用户授权位置
					uni.showModal({
						title: '位置权限',
						content: '为了更好地为您提供服务，需要获取您的位置信息。请在设置中开启位置权限。',
						confirmText: '去设置',
						cancelText: '取消',
						success: (res) => {
							if (res.confirm) {
								// 用户点击去设置，打开设置页面
								uni.openSetting({
									success: (settingRes) => {
										console.log('设置结果:', settingRes);
										if (settingRes.authSetting['scope.userLocation']) {
											// 用户已授权，重新获取位置
											this.getCurrentLocation();
										} else {
											// 用户仍未授权，使用默认位置
											this.useDefaultLocation();
										}
									},
									fail: () => {
										// 打开设置失败，使用默认位置
										this.useDefaultLocation();
									}
								});
							} else {
								// 用户选择使用默认位置
								this.useDefaultLocation();
							}
						}
					});
				}
			});
		},

		// 使用默认位置
		useDefaultLocation() {
			uni.showToast({
				title: '使用默认位置',
				icon: 'none'
			});
			this.initMarkers();
		},

		// 初始化地图标注点
		initMarkers() {
			this.markers = this.realmData.map((item) => {
				return {
					id: item.id,
					longitude: item.longitude,
					latitude: item.latitude,
					iconPath: item.realm_icon,
					width: 40,
					height: 40,
					anchor: {
						x: 0.5,
						y: 1
					},
					callout: {
						content: item.realm_name,
						color: '#333333',
						fontSize: 14,
						borderRadius: 8,
						bgColor: '#ffffff',
						padding: 8,
						display: 'ALWAYS',
						textAlign: 'center'
					}
				};
			});
			console.log('初始化标注点:', this.markers);
		},

		// 标注点点击事件
		onMarkerTap(e) {
			console.log('标注点被点击:', e);
			const markerId = e.detail.markerId;
			console.log('点击的标注点ID:', markerId);
			this.navigateToRealm(markerId);
		},

		// 气泡点击事件
		onCalloutTap(e) {
			console.log('气泡被点击:', e);
			const markerId = e.detail.markerId;
			console.log('点击的气泡ID:', markerId);
			this.navigateToRealm(markerId);
		},

		// 跳转到圈子详情
		navigateToRealm(markerId) {
			const realm = this.realmData.find(item => item.id === markerId);
			console.log('找到的圈子数据:', realm);
			if (realm) {
				// 先显示提示，确认点击有效
				uni.showToast({
					title: `进入${realm.realm_name}`,
					icon: 'none'
				});

				// 延迟跳转，让用户看到提示
				setTimeout(() => {
					uni.navigateTo({
						url: `/pages/packageF/realm_detail/index?realmId=${realm.id}&realmName=${encodeURIComponent(realm.realm_name)}`
					});
				}, 1000);
			} else {
				console.log('未找到对应的圈子数据');
				uni.showToast({
					title: '圈子信息不存在',
					icon: 'none'
				});
			}
		}
	}
}
</script>

<style scoped>
.map-container {
	width: 100%;
	height: 100%;
	top: 0;
    position: fixed;
}

.map {
	width: 100%;
	height: 100%;
}

.loading-mask {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(255, 255, 255, 0.8);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 999;
}

.loading-content {
	background-color: #ffffff;
	padding: 20px;
	border-radius: 8px;
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.loading-text {
	color: #333333;
	font-size: 16px;
}
</style>
